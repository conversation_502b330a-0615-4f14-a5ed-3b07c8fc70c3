<template>
  <Navigater :title="t('index.guajiangRecord')" />
  <div class="content">
    <div v-if="recordList.length === 0" class="nodata">
      <img src="/static/nodata.png" alt="" />
      <div class="text">{{ t('common.nodata') }}</div>
    </div>
    <div v-else class="recordList">
      <div v-for="(item, index) in recordList" :key="index" class="record-item">
        <div class="record-header">
          <div class="status" :class="item.status === 1 ? 'success' : 'fail'">
            {{ item.status === 1 ? t('index.guajiangSuccess') : t('index.guajiangFail') }}
          </div>
          <div class="date">{{ timestempToDate(item.updatetime * 1000) }}</div>
        </div>
        <div class="record-content">
          <div class="prize-name" :class="item.status === 1 ? 'success' : 'fail'">
            {{ item.name }}
          </div>
          <div v-if="item.status === 1" class="prize-icon">
            <img v-if="item.icon" :src="item.icon" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { getLotteryHistoryApi } from '@/api/index'
import { useI18n } from 'vue-i18n'
import { timestempToDate } from '@/common/common'

const { t } = useI18n()
const recordList = ref([])

// 获取抽奖历史
const getLotteryHistoryList = async () => {
  try {
    const res = await getLotteryHistoryApi()
    recordList.value = res.data || []
  } catch (error) {
    console.error('获取刮奖记录失败:', error)
    recordList.value = []
  }
}

onMounted(() => {
  getLotteryHistoryList()
})
</script>

<style lang="scss" scoped>
.content {
  padding: 1rem;
  height: calc(var(--vh) * 100 - 3.13rem);
  overflow-y: auto;
}

.nodata {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;

  img {
    width: 8rem;
    height: 8rem;
    opacity: 0.6;
    margin-bottom: 1rem;
  }

  .text {
    color: #999;
    font-size: 0.875rem;
  }
}

.recordList {
  .record-item {
    background: #fff;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #ddd;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;

    .status {
      padding: 0.25rem 0.75rem;
      border-radius: 1rem;
      font-size: 0.75rem;
      font-weight: 500;

      &.fail {
        background: #fff2f0;
        color: #ff4d4f;
      }
    }

    .date {
      color: #999;
      font-size: 0.75rem;
    }
  }

  .record-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .prize-name {
      font-size: 1rem;
      font-weight: 500;
      flex: 1;

      &.success {
        color: #52c41a;
      }

      &.fail {
        color: #ff4d4f;
      }
    }

    .prize-icon {
      width: 3rem;
      height: 3rem;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 0.5rem;
      }
    }
  }
}
</style>
