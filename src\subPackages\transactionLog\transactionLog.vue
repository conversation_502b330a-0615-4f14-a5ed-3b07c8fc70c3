<script setup lang="ts">
// import SearchNavigation from '@/components/searchNavigation/searchNavigation.vue'
import { onMounted, Ref, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import OrderLog from './components/orderLog.vue'
import IncomeLog from './components/incomeLog.vue'
import { getLiuShuiApi } from '@/api/transactionLog'
import { JyType, CzType } from '@/api/transactionLog/indexType'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'
import Navigator from '@/components/navigator/navigator.vue'
import { closeToast, showLoadingToast } from 'vant'
const { t } = useI18n()

interface TabType {
  title: string
  value: number
}
const isLs = ref(false)
onLoad((option) => {
  console.log(option, 'option')
  if (option?.isLs === '1') {
    isLs.value = true
    tabValue.value = 3
  } else {
    isLs.value = false
    tabValue.value = 1
  }
})

const tabValue: Ref<Number> = ref(1)

const tabList: Ref[TabType] = ref([
  {
    title: 'transactionLog.tab2',
    value: 1
  },
  {
    title: 'transactionLog.tab3',
    value: 2
  }
])

const checkTab = (value: number) => {
  tabValue.value = value
  jyPage.value = 1
  jyData.value = []
  czPage.value = 1
  czData.value = []
  tlPage.value = 1
  tlData.value = []
  getLiuShuiFn()
}

onMounted(() => {
  getLiuShuiFn()
})

const jyData: Ref[JyType] = ref([])
const jyPage = ref(1)
const jyLastPage = ref(1)
const czData: Ref[CzType] = ref([])
const czPage = ref(1)
const czLastPage = ref(1)
const tlData: Ref[CzType] = ref([])
const tlPage = ref(1)
const tlLastPage = ref(1)
const getLiuShuiFn = async () => {
  if (tabValue.value === 1 && czPage.value > czLastPage.value) {
    return
  } else if (tabValue.value === 2 && tlPage.value > tlLastPage.value) {
    return
  } else if (tabValue.value === 3 && jyPage.value > jyLastPage.value) {
    return
  }
  showLoadingToast({
    message: t('search.tip2'),
    mask: true,
    loadingType: 'spinner'
  })
  isLoading.value = true
  let page = 1
  switch (tabValue.value) {
    case 1:
      page = czPage.value
      break
    case 2:
      page = tlPage.value
      break
    case 3:
      page = jyPage.value
      break
  }
  const res = await getLiuShuiApi({ type: tabValue.value, page })
  closeToast()
  if (res.code === 1) {
    if (tabValue.value === 3) {
      jyLastPage.value = res.data.last_page
      if (jyPage.value <= jyLastPage.value) {
        jyPage.value++
      }
      res.data.data.forEach((element) => {
        const arr = element.detailed.split('|')
        element.name = arr[0]
        if (arr[1]) {
          element.gpCode = arr[2]
          element.gpName = arr[4]
          element.isshow = true
        } else {
          element.isshow = false
        }

        jyData.value.push(element)
      })
    }

    if (tabValue.value === 1) {
      czLastPage.value = res.data.last_page
      if (czPage.value <= czLastPage.value) {
        czPage.value++
      }
      res.data.data.forEach((element) => {
        czData.value.push(element)
      })
    }
    if (tabValue.value === 2) {
      tlLastPage.value = res.data.last_page
      if (tlPage.value <= tlLastPage.value) {
        tlPage.value++
      }
      res.data.data.forEach((element) => {
        tlData.value.push(element)
      })
    }
  }

  isLoading.value = false
}

const isLoading = ref(false)

onReachBottom(() => {})

const bottomFn = () => {
  getLiuShuiFn()
}
</script>

<template>
  <Navigator class="top" :title="isLs ? t('index.button4') : t('user.shouru')" />
  <view class="bg-[#fff]"> </view>
  <view v-if="!isLs" class="tab-box">
    <view v-for="item in tabList" :key="item.value" class="tab-box-item" :class="tabValue === item.value ? 'active' : ''" @click="checkTab(item.value)">
      {{ t(item.title) }}
    </view>
  </view>

  <OrderLog v-if="isLs && tabValue === 3" :data="jyData" @bottom="bottomFn" />
  <IncomeLog v-if="!isLs && tabValue === 1" :data="czData" :value="tabValue" @bottom="bottomFn" />
  <IncomeLog v-if="!isLs && tabValue === 2" :data="tlData" :value="tabValue" @bottom="bottomFn" />
</template>

<style lang="scss" scoped>
.list {
  background: rgb(10, 30, 54, 0.5);
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
}

.loading-box {
  display: flex;
  justify-content: center;
  align-content: center;
}
uni-page-body {
  height: 100%;
}
.tab-box {
  width: 100%;
  padding: 0.8125rem 1.3125rem;
  display: flex;
  align-items: center;
  gap: 0.63rem;
  .tab-box-item.active {
    background: $color-primary;
    color: $color-white;
    border: 0;
  }
  .tab-box-item {
    flex: 1;
    height: 2.75rem;
    padding: 0.25rem 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.875rem;
    color: $color-primary;
    border: 0.05rem solid $color-primary;
  }
}
</style>
