<template>
  <Navigator :title="t('tabbar.record')" :is-show-back="false" />
  <scroll-view scroll-y :style="{ height: `calc(${store.pageHeight} - 7.5rem)` }" @scrolltolower="getNews">
    <view class="box">
      <view class="record-list">
        <NotData v-if="!recordList.length" />
        <view v-for="item in recordList" :key="item.id" class="record-item" @click="onPage(item.id)">
          <div class="right">
            <view class="record-item-title"> {{ item.title }} </view>
            <view class="record-item-time">{{ formatTimestamp(item.updatetime * 1000) }}</view>
          </div>
          <img :src="item.image" alt="" @click.stop="previewImage(item.image)" />
        </view>
      </view>
    </view>
  </scroll-view>

  <CustomTabbar :id="3" />
</template>

<script lang="ts" setup>
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'
import Navigator from '@/components/navigator/navigator.vue'
import { useI18n } from 'vue-i18n'
// import SearchNavigation from '@/components/searchNavigation/searchNavigation.vue'
import { ref } from 'vue'
import { getNewsApi } from '@/api/record/record'
import { onLoad } from '@dcloudio/uni-app'
import type { Ref } from 'vue'
import { closeToast, showImagePreview, showLoadingToast } from 'vant'
import { useCounterStore } from '@/store/store'
const store = useCounterStore()
const { t } = useI18n()
interface recordType {
  id: number
  image: string
  title: string
  updateTime: number
}

const onPage = (id: number | string) => {
  uni.navigateTo({
    url: '/subPackages/details/details?id=' + id
  })
}

// 格式化时间
function formatTimestamp(timestamp: number | string) {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  // 月份从0开始，所以我们需要加1
  const month = ('0' + (date.getMonth() + 1)).slice(-2)
  const day = ('0' + date.getDate()).slice(-2)
  const hours = ('0' + date.getHours()).slice(-2)
  const minutes = ('0' + date.getMinutes()).slice(-2)

  return `${year}/${month}/${day} ${hours}:${minutes}`
}

const recordList: Ref[recordType] = ref([])

onLoad(() => {
  page = 1
  recordList.value = []
  getNews()
})

let page = 1

const getNews = async () => {
  showLoadingToast({
    message: t('toastText.tip1'),
    forbidClick: true,
    overlay: true,
    loadingType: 'spinner'
  })
  const res = await getNewsApi({ page })

  if (res.code === 1) {
    closeToast()
    page++
    // res.data.data.forEach((element) => {
    //   recordList.value.push(element)
    // })
    res.data.data.forEach((item) => {
      recordList.value.push(item)
    })
  }
}

const previewImage = (item) => {
  showImagePreview([item])
}
</script>
<style scoped lang="scss">
.box {
  height: calc(var(--vh) * 100 - 3.13rem - 4.35rem);
  .record-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 0.94rem;
    .record-item {
      position: relative;
      margin-bottom: 0.88rem;
      padding-left: 0.93rem;
      display: flex;
      gap: 1rem;
      width: 100%;
      &::before {
        content: '';
        height: 100%;
        width: 0.25rem;
        background: #27d7ff;
        border-radius: 0.19rem;
        left: 0;
        position: absolute;
      }
      img {
        width: 5rem;
        border-radius: 0.31rem;
        display: block;
      }
      .right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .record-item-title {
        font-weight: 500;
        font-size: 0.8125rem;
        color: $color-black;
      }

      .record-item-time {
        font-weight: 500;
        font-size: 0.8125rem;
        color: $color-gray;
        margin-top: 0.5rem;
      }
    }
  }

  uni-image {
    width: 100%;
    height: 4.5938rem;
  }
}
</style>
