<template>
  <Navigater :title="t('index.guajiang')" />
  <div class="container">
    <div class="bg">
      <div class="bgTitle"></div>
      <div class="canvasWrap">
        <canvas ref="bottomCanvas" canvas-id="bottomCanvas" class="bottomCanvas"></canvas>
        <canvas ref="topCanvas" canvas-id="topCanvas" class="topCanvas" @touchmove="touchmove"></canvas>
        <img class="jiangpin" :src="guajiangIcon" alt="" />
        <div class="center">{{ guajiangResult }}</div>
        <div class="bottom">
          <div class="left row" @click="recordWindow = true">
            <img src="/static/image/index/guajiang/gift.png" mode="" />
            <span>{{ t('index.guajiangRecord') }}</span>
          </div>
          <div class="right row">
            <img src="/static/image/index/guajiang/logo.png" mode="" />
          </div>
        </div>
      </div>
      <div class="bottomImage"></div>
    </div>
  </div>

  <van-popup v-model:show="recordWindow" position="bottom" class="recordWindow">
    <div class="close" @click="recordWindow = false">
      <van-icon name="cross" color="#fff" />
    </div>
    <div class="title">{{ t('index.guajiangRecord') }}</div>
    <div v-if="recordList.length === 0" class="nodata">{{ t('common.nodata') }}</div>
    <div class="recordList">
      <div v-for="(item, index) in recordList" :key="index" class="row">
        <div class="left">{{ item.status === 1 ? t('index.guajiangSuccess') : t('index.guajiangFail') }}</div>
        <div class="center" :class="item.status === 1 ? 'success' : 'fail'">{{ item.name }}</div>
        <div class="right">{{ timestempToDate(item.updatetime * 1000) }}</div>
      </div>
    </div>
  </van-popup>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { getLotteryHistoryApi, getLotteryDrawApi, getLotteryCountApi } from '@/api/index'
import { onLoad } from '@dcloudio/uni-app'
import weigua from '@/static/image/index/guajiang/weigua.png'
import yigua from '@/static/image/index/guajiang/yigua.png'
import { useI18n } from 'vue-i18n'
import { timestempToDate } from '@/common/common'
const { t } = useI18n()

const guajiangStatus = ref(false)
const recordWindow = ref(false)
const guajiangResult = ref('')
const guajiangIcon = ref('')
const recordList = ref([])
const guajiangCount = ref(0)
const showCount = ref(0)

onLoad(() => {
  getLotteryHistoryList()
  getLotteryCount()
  initBottomCanvas()
  initTopCanvas()
})

const bottomCtx = ref(null)
const bottomCanvas = ref(null)
const topCtx = ref(null)
const topCanvas = ref(null)

const initBottomCanvas = () => {
  setTimeout(() => {
    // 底图
    bottomCtx.value = uni.createCanvasContext('bottomCanvas')
    const backgroundImage = new Image()
    backgroundImage.src = yigua
    backgroundImage.onload = function () {
      bottomCtx.value.drawImage(backgroundImage.currentSrc, 0, 0, bottomCanvas.value.$el.clientWidth, bottomCanvas.value.$el.clientHeight)
      bottomCtx.value.draw()
    }
  }, 0)
}
const initTopCanvas = () => {
  setTimeout(() => {
    // 顶部图
    topCtx.value = uni.createCanvasContext('topCanvas')
    const backgroundImage = new Image()
    backgroundImage.src = weigua
    backgroundImage.onload = function () {
      topCtx.value.drawImage(backgroundImage.currentSrc, 0, 0, topCanvas.value.$el.clientWidth, topCanvas.value.$el.clientHeight)
      topCtx.value.draw()
    }
  }, 0)
}

const touchmove = (e) => {
  if (!guajiangStatus.value) {
    guajiang()
    guajiangStatus.value = true
  }
  if (guajiangCount.value === 0) {
    return
  }
  const x = e.changedTouches[0].x
  const y = e.changedTouches[0].y
  clearArcFun(x, y, 30, topCtx.value)
  topCtx.value.draw(true)
}

const clearArcFun = (x, y, r, cxt) => {
  // (x,y)为要清除的圆的圆心，r为半径，cxt为context
  let stepClear = 1 // 别忘记这一步
  clearArc(x, y, r)
  function clearArc(x, y, radius) {
    const calcWidth = radius - stepClear
    const calcHeight = Math.sqrt(radius * radius - calcWidth * calcWidth)

    const posX = x - calcWidth
    const posY = y - calcHeight

    const widthX = 2 * calcWidth
    const heightY = 2 * calcHeight

    if (stepClear <= radius) {
      cxt.clearRect(posX, posY, widthX, heightY)
      stepClear += 1
      clearArc(x, y, radius)
    }
  }
}

// // 获取抽奖历史
const getLotteryHistoryList = async () => {
  const res = await getLotteryHistoryApi()

  recordList.value = res.data
}

// // 获取抽奖次数
const getLotteryCount = async () => {
  const res = await getLotteryCountApi()
  console.log(res)
  guajiangCount.value = res.data.count
  showCount.value = res.data.count
}

const guajiang = async () => {
  showCount.value = 0
  guajiangStatus.value = false
  guajiangResult.value = ''
  guajiangIcon.value = ''
  const res = await getLotteryDrawApi()
  if (res.code === 1) {
    guajiangResult.value = res.data?.product?.name
    guajiangIcon.value = res.data?.product?.icon
    getLotteryHistoryList()
    // getLotteryCount()
  }
}
</script>

<style lang="scss" scoped>
.container {
  overflow: hidden;
  height: calc(var(--vh) * 100 - 3.13rem);
  touch-action: none;
}
.bg {
  background: url('@/static/image/index/guajiang/bg.png');
  height: calc(var(--vh) * 100);
  background-size: 100% 100%;
  background-position-y: -3.13rem;
  background-repeat: no-repeat;
  overflow: hidden;
  position: relative;
  .logo {
    margin: 0 1.25rem 0;
    display: flex;
    justify-content: flex-end;
    img {
      width: 100%;
      height: 2.38rem;
      object-fit: contain;
    }
  }
  .bgTitle {
    background: url('@/static/image/index/guajiang/title.png');
    width: calc(100vw - 2.5rem);
    height: 9.25rem;
    object-fit: contain;
    padding: 0 1.25rem;
    background-size: 100% auto;
    background-repeat: no-repeat;
    margin: 1vh auto 0;
  }
  .count {
    text-align: center;
    color: $color-primary;
    margin: 1rem 0;
    font-weight: 900;
    .num {
      color: #666;
      margin: 0 0.31rem;
    }
  }
  .bottomImage {
    background: url('@/static/image/index/guajiang/bottomImage.png');
    background-size: 100%;
    background-position: top;
    background-repeat: no-repeat;
    width: 100vw;
    height: calc(var(--vh) * 30);
    position: fixed;
    bottom: 0;
  }
}

.content {
  position: relative;
  display: none;
  img {
    margin: 0 0.5rem;
    width: calc(100vw - 1rem);
  }
  .center {
    position: absolute;
    text-align: center;
    width: 80%;
    left: 10%;
    color: #8ebbff;
    font-size: 1.63rem;
    font-weight: bold;
    top: 40%;
  }
}

.canvasWrap {
  overflow: hidden;
  margin: 1rem 0 0.5rem;
  width: calc(100vw);
  height: calc((100vw) * 1.092);
  position: relative;
  touch-action: manipulation;
  background: url('@/static/image/index/guajiang/wrap.png');
  background-size: 100% 100%;
  canvas {
    width: 19.89rem;
    height: 15.94rem;
    left: 1.75rem;
    top: 1.13rem;
    position: absolute;
  }
  .bottomCanvas {
    z-index: 10;
  }
  .topCanvas {
    z-index: 100;
  }
  .center {
    display: none;
    position: absolute;
    text-align: center;
    width: 80%;
    left: 10%;
    color: #8ebbff;
    font-size: 1.63rem;
    font-weight: bold;
    top: 43%;
    z-index: 50;
  }
  .jiangpin {
    position: absolute;
    text-align: center;
    width: 80%;
    object-fit: contain;
    left: 10%;
    color: #8ebbff;
    font-size: 1.63rem;
    font-weight: bold;
    top: 10%;
    z-index: 50;
  }
}

.bottom {
  display: flex;
  margin-top: 20rem;
  justify-content: space-between;
  position: relative;
  z-index: 1000;
  .row {
    display: flex;
    align-items: center;
    margin: 1rem;
    gap: 0.3rem;
    span {
      color: $color-white;
    }
  }
  .left {
    img {
      width: 1.44rem;
      height: 1.44rem;
    }
  }
  .right {
    img {
      width: 6.38rem;
      object-fit: contain;
      // height: 1.44rem;
    }
  }
}

.window {
  width: 21.56rem;
  height: 16.25rem;
  border-radius: 1rem;
  padding: 1.38rem 1.56rem;
  .title {
    font-size: 1.13rem;
    color: #004eaa;
    text-align: center;
  }
  .center {
    color: #8ebbff;
    font-size: 1.63rem;
    font-weight: bold;
    margin: 2.31rem auto 3.88rem;
    text-decoration: underline;
    text-align: center;
  }
  .button {
    width: 100%;
    height: 2.75rem;
    line-height: 2.75rem;
    background: #004eaa;
    border-radius: 1.38rem;
    text-align: center;
    font-weight: bold;
    color: #fff;
    font-size: 1.13rem;
  }
}

.recordWindow {
  height: calc(var(--vh) * 70);
  background: #ffffff10;
  backdrop-filter: blur(0.63rem);
  border-radius: 1.25rem 1.25rem 0 0;
  .close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    .van-icon {
      font-size: 1.33rem;
      font-weight: bold;
    }
  }
  // .title {
  //   margin: 1.88rem auto 1.56rem;
  //   text-align: center;
  //   color: #ffecb9;
  //   font-weight: bold;
  //   font-size: 1.13rem;
  //   position: relative;
  //   &::before {
  //     content: '';
  //     position: absolute;
  //     width: 3.5rem;
  //     height: 0.81rem;
  //     background: url('@/static/image/index/guajiang/right.png');
  //     background-size: 100% 100%;
  //     left: 23vw;
  //     top: 0.45rem;
  //   }
  //   &::after {
  //     content: '';
  //     position: absolute;
  //     width: 3.5rem;
  //     height: 0.81rem;
  //     background: url('@/static/image/index/guajiang/left.png');
  //     background-size: 100% 100%;
  //     left: 62vw;
  //     top: 0.45rem;
  //   }
  // }

  .recordList {
    height: 51vh;
    overflow: auto;
    .row {
      display: flex;
      justify-content: space-between;
      padding: 0 1rem;
      height: 2.75rem;
      align-items: center;
      .left {
        color: #fff;
        font-size: 0.81rem;
      }
      .center {
        max-width: 50vw;
        font-size: 0.94rem;
      }
      .right {
        color: #fff;
        font-size: 0.7rem;
      }
      .success {
        color: #8ebbff;
      }
      .fail {
        color: #e80004;
      }
    }
  }

  .nodata {
    color: #888;
    font-size: 1.13rem;
  }
}
</style>
