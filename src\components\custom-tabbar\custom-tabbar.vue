<template>
  <view class="tabbar">
    <view v-for="item in list" :key="item.id" class="bar" @click="switchTab(item)">
      <view class="card" :class="{ active: props.id == item.id }">
        <image class="image" :src="props.id == item.id ? item.selectIcon : item.icon"></image>
        <view class="text">{{ item.text }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lange="ts">
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const url = '../../static/image/tabbar/'
const props = defineProps({
  id: {
    type: Number,
    default: 0
  }
})
const text1 = computed(() => t('tabbar.index'))
const text2 = computed(() => t('tabbar.market'))
const text3 = computed(() => t('tabbar.jiaoyi'))
const text4 = computed(() => t('tabbar.record'))
const text5 = computed(() => t('tabbar.user'))
const list = ref([
  {
    path: '/pages/index/index',
    icon: `${url}index.png`,
    selectIcon: `${url}index_select.png`,
    text: text1,
    id: 0
  },
  {
    path: '/pages/market/market',
    icon: `${url}market.png`,
    selectIcon: `${url}market_select.png`,
    text: text2,
    id: 1
  },
  {
    path: '/pages/jiaoyi/jiaoyi',
    icon: `${url}jiaoyi.png`,
    selectIcon: `${url}jiaoyi_select.png`,
    text: text3,
    id: 2
  },
  {
    path: '/pages/record/record',
    icon: `${url}record.png`,
    selectIcon: `${url}record_select.png`,
    text: text4,
    id: 3
  },
  {
    path: '/pages/user/user',
    icon: `${url}user.png`,
    selectIcon: `${url}user_select.png`,
    text: text5,
    id: 4
  }
])

const switchTab = (item) => {
  if (item.path) {
    uni.switchTab({
      url: item.path
    })
  }
}
</script>

<style scoped lang="scss">
.isIphoneX {
  padding-bottom: 2.13rem !important;
}
.tabbar {
  width: 100%;
  height: 4.375rem;
  display: flex;
  justify-content: space-around;
  position: fixed;
  z-index: 1000;
  bottom: 0;
  left: 0;
  // border-top: 0.03rem solid #3d3d3d;
  // box-shadow: 0 0.53rem 0.85rem 0.13rem #00000060;

  box-shadow: 0 -0.25rem 0.25rem 0 rgba(0, 0, 0, 0.1);
  .bar {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card {
    width: 3.59rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 3.75rem;
    height: 4.375rem;
    .image {
      width: 1.88rem;
      height: 1.88rem;
      padding: 0.1rem;
    }
    .text {
      font-size: 0.69rem;
      color: #707070;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      word-break: break-all;
    }
  }
  .active {
    width: 3.75rem;
    height: 4.375rem;
    position: relative;
    .text {
      color: $color-primary;
    }
  }
}
</style>
