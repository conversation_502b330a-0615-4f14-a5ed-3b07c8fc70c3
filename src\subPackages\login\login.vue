<template>
  <view class="container">
    <view class="wrap">
      <img class="bg" :src="bg" />
      <view class="title">{{ t('login.tip3') }}</view>
      <view class="mas">
        <view class="input_wrap" @click="phoneFocus = true">
          <input v-model="loginParams.account" :placeholder="t('login.account_placeholder')" :focus="phoneFocus" @blur="phoneFocus = false" />
        </view>
        <view class="input_wrap" @click="passwordFocus = true">
          <input v-model="loginParams.password" :placeholder="t('login.password_placeholder')" :type="showPassword ? 'text' : 'password'" :focus="passwordFocus" @blur="passwordFocus = false" />
          <img class="pwd" :src="showPassword ? eye : eye_close" alt="" @click.stop="showPassword = !showPassword" />
        </view>
      </view>
      <view class="button-wrap">
        <view class="button" @click="login">{{ t('login.button_text') }}</view>
        <view class="text" @click="reLaunch('/subPackages/register/register')">{{ t('login.register') }}</view>
        <view class="button circle_button" @click="reLaunch('/subPackages/register/register')">{{ t('register.tip4') }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { loginApi } from '@/api/login/login'
import { switchTab, reLaunch, checkInput } from '@/common/common'
import { useCounterStore } from '@/store/store'
import { showSuccessToast } from 'vant'
import bg from '@/static/image/login/logo3.png'
import eye from '@/static/image/login/eye.png'
import eye_close from '@/static/image/login/eye-close.png'
import { onShow } from '@dcloudio/uni-app'
const { t } = useI18n()
const store = useCounterStore()

const loginParams = ref({
  account: '',
  password: ''
})

const showPassword = ref(false)
const phoneFocus = ref(false)
const passwordFocus = ref(false)

const checkArr = [
  { key: 'account', message: t('login.account_error') },
  { key: 'password', message: t('login.password_error') }
]

onShow(() => {
  loginParams.value.account = uni.getStorageSync('account') || ''
  loginParams.value.password = uni.getStorageSync('password') || ''
})

const login = async () => {
  if (!checkInput(checkArr, loginParams.value)) {
    return
  }
  uni.showLoading({ mask: true })
  const res = await loginApi(loginParams.value)
  uni.hideLoading()
  if (res.code === 1) {
    uni.setStorageSync('token', res.data.userinfo.token)
    uni.setStorageSync('userId', res.data.userinfo.id)
    uni.setStorageSync('userInfo', res.data.userinfo)
    uni.setStorageSync('account', loginParams.value.account)
    uni.setStorageSync('password', loginParams.value.password)
    store.token = res.data.userinfo.token
    store.userId = res.data.userinfo.userId
    store.getUserInfo()
    showSuccessToast(res.msg)
    setTimeout(() => {
      switchTab('/pages/index/index')
    }, 1000)
  }
}
</script>

<style lang="scss" scoped>
.navigator {
  width: 100%;
  height: 3.13rem;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 1rem;
  .van-icon {
    transform: rotate(180deg);
  }
}
body {
  height: 100vh;
  background: $color-white;
}

.wrap {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: calc(var(--vh) * 100);
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: url('@/static/image/login/bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .bg {
    width: 6.25rem;
    height: 6.25rem;
    margin: 2.25rem auto 0;
    border-radius: 0.63rem;
  }
  .title {
    color: $color-black;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 2rem 0 1.88rem;
  }
}
.name {
  margin: 2.3vh auto 9.54vh;
  font-size: 1.5rem;
  color: $uni-color-primary;
}
.button-wrap {
  width: 20rem;
  margin-top: 1.875rem;
  .text {
    color: $color-black;
    font-size: 0.9375rem;
    line-height: 3.125rem;
  }
  .button {
    height: 3.75rem;
    border-radius: 0.63rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: $color-white;
    background: $color-primary;
    font-weight: 500;
    font-size: 1.13rem;
  }
  .circle_button {
    border: 0.06rem solid $color-primary;
    background: transparent !important;
    color: $color-primary;
  }
}
.mas {
  width: 20rem;
  height: 7.375rem;
  border-radius: 1.25rem;
}
.input_wrap {
  display: flex;
  align-items: center;
  height: 3.06rem;
  border-radius: 0.75rem;
  margin-bottom: 1.25rem;
  background: #f4f4f4;
  border: 0.03rem solid $color-primary;
  padding: 0 0.88rem;
  max-width: 20rem;
  margin-left: auto;
  margin-right: auto;
  font-size: 1rem;
  gap: 1rem;
  input {
    flex: 1;
    text-align: left;
    .uni-input-placeholder {
      color: $color-gray;
    }
  }
  .pwd {
    padding: 0.2rem;
  }
  img {
    width: 1.5rem;
    height: 1.5rem;
    object-fit: contain;
  }
}
</style>
