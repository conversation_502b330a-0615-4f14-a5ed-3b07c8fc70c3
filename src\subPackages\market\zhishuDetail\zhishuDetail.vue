<template>
  <Navigater v-if="isShow" :title="`${gupiaoDetail.name}`"> </Navigater>
  <view v-if="isShow" class="content" :style="{ height: contentHeight }">
    <div class="head">
      <div class="left">
        <div class="title">{{ gupiaoDetail.name }}</div>
        <div class="daima">{{ gupiaoDetail.zimudaima }}</div>
      </div>
      <div class="right">
        <div class="price" :class="getColor(gupiaoDetail)">{{ gupiaoDetail.price }}</div>
      </div>
    </div>
    <view class="zd">
      <view class="text-primary text-lg">
        <text v-if="gupiaoDetail.zhangdiebaifenbi === 0"> - </text>
        <van-icon v-else :class="getColor(gupiaoDetail, true)" name="down" size=".9rem" />
      </view>
      <view class="text-lg" :class="getColor(gupiaoDetail)">({{ gupiaoDetail.zhangdiebaifenbi }}%)</view>
    </view>
    <view class="card">
      <view class="flex justify-between">
        <view class="flex flex-1 text-left de" style="width: 9.375rem">
          <view class="text-lg text-gray hh2">{{ t('gupiaoDetail.label1') }}：</view>
          <view class="text-lg" style="color: #141414">{{ gupiaoDetail.open }}</view>
        </view>
        <view class="flex flex-1 text-left de2" style="width: 9.375rem">
          <view class="text-lg text-gray hh2">{{ t('gupiaoDetail.label2') }}：</view>
          <view class="text-lg" style="color: #141414">{{ gupiaoDetail.close }}</view>
        </view>
      </view>
      <view class="flex justify-between">
        <view class="flex flex-1 text-left de" style="width: 9.375rem">
          <view class="text-lg text-gray hh2">{{ t('gupiaoDetail.label3') }}：</view>
          <view class="text-lg" style="color: #141414">{{ gupiaoDetail.high }}</view>
        </view>
        <view class="flex flex-1 text-left de2" style="width: 9.375rem">
          <view class="text-lg text-gray hh2">{{ t('gupiaoDetail.label4') }}：</view>
          <view class="text-lg" style="color: #141414">{{ gupiaoDetail.low }}</view>
        </view>
      </view>
      <!-- <view class="flex justify-between">
        <view class="flex flex-1 text-left de" style="width: 9.375rem">
          <view class="text-lg text-gray hh2">{{ t('gupiaoDetail.label5') }}：</view>
          <view class="text-lg" style="color: #fff">{{ gupiaoDetail.vol }}</view>
        </view>
        <view class="flex flex-1 text-left de2" style="width: 9.375rem">
          <view class="text-lg text-gray hh2">{{ t('gupiaoDetail.label6') }}：</view>
          <view class="text-lg" style="color: #fff">{{ gupiaoDetail.totalMoney }}({{ t('gupiaoDetail.yi') }})</view>
        </view>
      </view> -->
    </view>

    <view class="date_wrap">
      <div v-for="(item, index) in columns" :key="index" :class="{ active: item.value == dateActive }" class="date">
        <span @click="getChart(gupiaoDetail.id, item.value)">{{ item.label }}</span>
        <div v-if="index < columns.length - 1" class="line"></div>
      </div>
    </view>
    <view class="chart_wrap">
      <GupiaoChart :value="chartValue" />
    </view>
  </view>
</template>
<script setup lang="ts">
import GupiaoChart from '@/subPackages/index/gupiaoDetail/components/GupiaoChart.vue'
import { useCounterStore } from '@/store/store'
import { useI18n } from 'vue-i18n'
import { getTickerKApi } from '@/api/index/index'
import { timestempToDate, getColor } from '@/common/common'
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { getZhishuDetailApi } from '@/api/market/market'
const { t } = useI18n()
// 页面基础配置
const store = useCounterStore()
console.log(store)
const contentHeight = `calc(${store.pageHeight} - 3.125rem)`

onLoad(async (option) => {
  await getGupiaoDetail(option.id)
  getChart(option.id, dateActive.value)
})

// 获取股票详情
const gupiaoDetail = ref({
  is_zixuan: 0,
  price: '-'
})
const isShow = ref(false)
const getGupiaoDetail = async (id: any) => {
  const res = await getZhishuDetailApi({ id })
  isShow.value = true
  res.data.time = timestempToDate(res.data.updatetime * 1000, '/')
  res.data.totalMoney = Math.floor(((res.data.vol * res.data.price * 1000) / 100000000) * 100) / 100
  gupiaoDetail.value = res.data
}

const columns = [
  { label: t('gupiaoDetail.tip3'), value: 1 },
  { label: t('gupiaoDetail.tip4'), value: 2 },
  { label: t('gupiaoDetail.tip5'), value: 3 },
  { label: t('gupiaoDetail.tip6'), value: 4 },
  { label: t('gupiaoDetail.tip7'), value: 5 },
  { label: t('gupiaoDetail.tip8'), value: 6 },
  { label: t('gupiaoDetail.tip9'), value: 7 },
  { label: t('gupiaoDetail.tip10'), value: 8 }
]

// 股票K线
const dateActive = ref(1)

const chartValue = ref({})
const getChart = async (id: number, kline_type: number = 0) => {
  dateActive.value = kline_type

  const res = await getTickerKApi({ kline_type: dateActive.value, id, type: 2 })
  const kLineList = res.data.map((item: any) => {
    return {
      close: item.c,
      high: item.h,
      low: item.l,
      open: item.o,
      timestamp: item.t * 1000,
      volume: item.v
    }
  })
  chartValue.value = kLineList
}
</script>

<style scoped lang="scss">
.hh {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  word-break: break-all;
}
.collect {
  position: absolute;
  right: 0;
  height: 100%;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  image {
    width: 1.31rem;
    height: 1.31rem;
  }
}
.de {
  display: flex;
  align-items: center;
}
.text-left {
  text-align: left;
}
button {
  font-family: none;
}
.content {
  overflow: auto;
  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.7rem 0.94rem 0;
    .title {
      font-size: 0.94rem;
      color: $color-black;
      font-weight: 500;
    }
    .daima {
      font-size: 0.75rem;
      color: #aeaeae;
    }
    .price {
      font-size: 1.5rem;
      font-weight: 500;
      color: $color-black;
    }
  }
  .box {
    width: 23.4375rem;
    height: 17.1875rem;
    border-top-left-radius: 1.5625rem;
    border-top-right-radius: 1.5625rem;
    background: #fff;
    z-index: 9;
  }

  .zd {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    text-align: center;
    margin: 0 0.94rem;
  }

  .jiage {
    margin-top: 0.7188rem;
    text-align: center;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 500;
  }
  .card {
    margin: 0.5938rem auto 0;
    padding: 0 0.9375rem;
    border-radius: 1.03rem;
    margin-top: 0.34rem;
    text-align: left;

    .text-lg {
      color: #fff;
    }

    .text-gray {
      color: #a6abb5;
    }
    .de2 {
      display: flex;
      justify-content: flex-end;
      height: 1.13rem;
      margin: 0.2rem 0;
    }
  }
  .date_wrap {
    height: 2.13rem;
    margin: 0.9375rem 0.84rem 0;
    padding: 0.3125rem 0;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.34rem;
    background: $color-white;
    .date {
      text-align: center;
      position: relative;
      display: flex;
      align-items: center;
      span {
        width: 2.2rem;
        height: 1.5rem;
        border-radius: 0.63rem;
        line-height: 1.5rem;
        font-size: 0.75rem;
        color: $color-black;
        transition: width 0.3s;
      }
    }
    .line {
      width: 0.06rem;
      height: 0.94rem;
      background: #aeaeae;
      margin: 0 0.2rem;
      border-radius: 0.19rem;
    }
    .active {
      span {
        width: 3rem;
        background-color: $color-primary;
        color: #fff;
      }
    }
  }
  .chart_wrap {
    padding: 0.56rem 1.25rem 2.56rem;
    margin-top: 0.34rem;
  }
  .button_wrap {
    padding: 1.25rem 1.25rem 0.625rem;
    display: flex;
    gap: 0.63rem;
    .button {
      border: 0.06rem solid #e3e8ef;
      color: $color-primary;
    }
    .active {
      background-color: $color-primary;
      color: #fff;
    }
  }
  .button {
    width: 10rem;
    height: 2.44rem;
    line-height: 2.44rem;
    text-align: center;
    color: #fff;
    font-size: 0.9375rem;
    background: #fff;
    border-radius: 0.5rem;
  }
  .submit {
    height: 3.0625rem !important;
    line-height: 3.0625rem !important;
    width: 100% !important;
    margin-top: 0.63rem !important;
    background-color: $color-primary;
    color: #fff;
  }
  .submit_wrap {
    background-color: #fff;
    border-radius: 1.03rem;
    padding: 0 1.56rem 1.25rem;
    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0.63rem 0;
      .title {
        font-size: 0.8125rem;
        color: #141316;
      }
    }
    .input_wrap {
      .van-field {
        width: 15.0625rem;
        height: 2.0625rem;
        display: flex;
        align-items: center;
        border-radius: 1.25rem;
        border: 0.03rem solid #d2d2f8;
        padding: 0 0.88rem;
        margin-left: auto;
        margin-right: auto;
        font-size: 1rem;
        margin: 0.38rem 0 0.59rem;
      }
    }
    .button_wrap {
      padding: 0;
    }
  }
}

.buy_window {
  background-color: #f4ffff;
  padding: 0 0.81rem 1.25rem 0.81rem;
  .row_group {
    display: flex;
    flex-wrap: wrap;
    .row {
      width: 50%;
    }
  }
  .title {
    text-align: center;
    padding: 0.8rem 0 0.5rem;
    font-size: 1rem;
  }
  .row {
    display: flex;
    justify-content: space-between;
    padding: 0.64rem 0.56rem;
    view {
      color: $color-black;
      font-size: 0.81rem;
    }
    .label {
      color: $color-gray;
    }
    .value {
      color: $color-black;
    }
  }
  .title {
    justify-content: center;
  }
  .button {
    height: 3.06rem;
    line-height: 3.06rem;
    width: 100%;
    text-align: center;
    border-radius: 0.8rem;
    background-color: $color-primary;
    color: $color-black;
    font-size: 0.94rem;
    margin-top: 1.25rem;
    font-weight: 500;
    color: #fff;
  }
  .close {
    position: absolute;
    width: 2.75rem;
    height: 2.75rem;
    padding: 1rem;
    right: 0.5rem;
    top: 0rem;
  }
}

::v-deep .van-popup--round {
  border-radius: 1.88rem 1.88rem 0 0 !important;
}
::v-deep .van-stepper__minus,
::v-deep .van-stepper__plus {
  width: 1.63rem;
  height: 1.63rem;
  border: 0.06rem solid #7d8729;
  border-radius: 50%;
  background: transparent;
  &::before {
    background: #7d8729;
  }
  &::after {
    background: #7d8729;
  }
}
::v-deep .van-stepper__minus--disabled,
::v-deep .van-stepper__plus--disabled {
  border-color: #ccc;
  &::before {
    background: #ccc;
  }
  &::after {
    background: #ccc;
  }
}

::v-deep .van-stepper__input {
  background: transparent;
  margin: 0 0.63rem;
  width: 2.6rem;
}
</style>
