import request from '@/utils/http'
import config from '@/utils/config'
import { getTickerKType, idType, buyGupiaoType, getJiaoyiDetailType, shengouType, buyDadanType, buyHongliType, buyGendanType, sellGendanType, rongziType } from './indexType'

// 热门股票
export function getXinguApplyApi() {
  return request({
    url: `${config.baseURL}/jiaoyi_list/xinguApply`,
    method: 'get'
  })
}
// 热门股票
export function getHotGupiaoApi() {
  return request({
    url: `${config.baseURL}/index/popular_product`,
    method: 'get'
  })
}

export function check_token() {
  return request({
    url: `${config.baseURL}/index/check_token`,
    method: 'get'
  })
}

// 股票K线
export function getTickerKApi(params: getTickerKType) {
  return request({
    url: `${config.baseURL}/product/getTickerK`,
    method: 'get',
    params
  })
}

// 股票详情
export function getGupiaoDetailApi(params: idType) {
  return request({
    url: `${config.baseURL}/product/getGupiaoDetail`,
    method: 'get',
    params
  })
}

// 收藏股票
export function collectApi(data: idType) {
  return request({
    url: `${config.baseURL}/index/addZixuan`,
    method: 'post',
    data
  })
}

// 收藏股票详情
export function collectDetailApi(params: idType) {
  return request({
    url: `${config.baseURL}/index/isZixuan`,
    method: 'get',
    params
  })
}

// 获取手续费
export function getUserMoneyApi() {
  return request({
    url: `${config.baseURL}/user/getUserMoney`,
    method: 'post'
  })
}

// 收藏股票
export function buyGupiaoApi(data: buyGupiaoType) {
  return request({
    url: `${config.baseURL}/index/buyGupiao`,
    method: 'post',
    data
  })
}

// 交易列表
export function getJiaoyiListApi() {
  return request({
    url: `${config.baseURL}/jiaoyi_list/getList`,
    method: 'get'
  })
}

// 交易详情
export function getJiaoyiDetailApi(params: getJiaoyiDetailType) {
  return request({
    url: `${config.baseURL}/index/getOrderDetail`,
    method: 'get',
    params
  })
}

// 賣出股票
export function sellGupiaoApi(data: getJiaoyiDetailType) {
  return request({
    url: `${config.baseURL}/index/PingCang`,
    method: 'post',
    data
  })
}

// 撤单
export function cancelOrdeApi(data: getJiaoyiDetailType) {
  return request({
    url: `${config.baseURL}/index/cancel_order`,
    method: 'post',
    data
  })
}

// 新股列表
export function getXinguListApi() {
  return request({
    url: `${config.baseURL}/index/getXingu`,
    method: 'get'
  })
}

// 申购股票
export function shengouApi(data: shengouType) {
  return request({
    url: `${config.baseURL}/index/shengouXingu`,
    method: 'post',
    data
  })
}

// 大單監控
export function getDadanListApi() {
  return request({
    url: `${config.baseURL}/index/monitoring_list`,
    method: 'get'
  })
}

// 大單監控
export function getUserMonitoringApi() {
  return request({
    url: `${config.baseURL}/index/user_monitoring`,
    method: 'get'
  })
}

// 購買大單
export function buyDadanApi(data: buyDadanType) {
  return request({
    url: `${config.baseURL}/index/buy_monitoring`,
    method: 'post',
    data
  })
}

// 紅利股票
export function getHongliListApi() {
  return request({
    url: `${config.baseURL}/index/get_discount_gupiao`,
    method: 'get'
  })
}

// 購買红利
export function buyHongliApi(data: buyHongliType) {
  return request({
    url: `${config.baseURL}/index/add_discount_gupiao`,
    method: 'post',
    data
  })
}

// 智慧跟单
export function getGendanListApi() {
  return request({
    url: `${config.baseURL}/index/follow_list`,
    method: 'get'
  })
}

// 購買跟單
export function buyGendanApi(data: buyGendanType) {
  return request({
    url: `${config.baseURL}/index/follow_order`,
    method: 'post',
    data
  })
}

// 智慧跟单記錄
export function getGendanRecordListApi() {
  return request({
    url: `${config.baseURL}/index/user_follow_record`,
    method: 'get'
  })
}

// 賣出跟單
export function sellGendanApi(data: sellGendanType) {
  return request({
    url: `${config.baseURL}/index/sell_follow_record`,
    method: 'post',
    data
  })
}

// 融资状态
export function getRongziApi() {
  return request({
    url: `${config.baseURL}/index/applicationStatus`,
    method: 'get'
  })
}

// 申请融资
export function rongziApi(data: rongziType) {
  return request({
    url: `${config.baseURL}/index/saveCA`,
    // url: `${config.baseURL}/CreditApplication/saveCA`,
    method: 'post',
    data
  })
}

// 奖品列表
export function getLotteryApi() {
  return request({
    url: `${config.baseURL}/index/lottery_prize`,
    method: 'get'
  })
}

// 抽奖次数
export function getLotteryCountApi() {
  return request({
    url: `${config.baseURL}/index/lottery_count`,
    method: 'get'
  })
}

// 抽奖结果
export function getLotteryDrawApi() {
  return request({
    url: `${config.baseURL}/index/lottery_draw`,
    method: 'get'
  })
}

// 抽奖历史
export function getLotteryHistoryApi() {
  return request({
    url: `${config.baseURL}/index/lottery_history`,
    method: 'get'
  })
}
