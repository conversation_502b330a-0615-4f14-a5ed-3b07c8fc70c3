<script setup lang="ts">
import { useI18n } from 'vue-i18n'
// import { showToast } from 'vant'

const { t } = useI18n()
// const searchValue = ref('')
// const onSearch = () => {}
// const onCancel = () => showToast('取消')

const goPage = () => {
  uni.navigateTo({
    url: '/subPackages/search/search'
  })
}
</script>

<template>
  <div class="search">
    <view class="nav-box">
      <view class="input_wrap" @click="goPage">
        <image src="/static/image/index/search.png" class="search-icon"></image>
        <span>{{ t('search.tip3') }}</span>
      </view>
    </view>
  </div>
</template>

<style lang="scss" scoped>
.uni-input-placeholder {
  font-size: 0.82rem !important;
}
.search {
  overflow: hidden;
}
.nav-box {
  top: 0;
  left: 0;
  // width: 100%;
  height: 3.125rem;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  padding: 0.53rem 0;
  margin: 0.53rem 0.94rem;
  .input_wrap {
    width: 14.375rem;
    flex: 1;
    height: 2.75rem;
    border-radius: 0.63rem;
    background: #f4f4f4;
    border: 0.03rem solid $color-primary;
    color: #fff;
    padding: 0 0.94rem;
    font-size: 0.82rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    .search-icon {
      width: 1rem;
      height: 1rem;
    }
    span {
      color: #aeaeae;
    }
  }

  .search-img {
    margin-left: 0.44rem;
    width: 0.875rem;
    height: 0.875rem;
  }

  ::v-deep.uni-input-input {
    color: #b3b3b3 !important;
    font-size: 0.82rem !important;
  }

  uni-input {
    flex: 1;
  }

  .set-box {
    width: 1.3125rem;
    height: 1.1875rem;
    margin-left: 0.5938rem;
  }
}
</style>
