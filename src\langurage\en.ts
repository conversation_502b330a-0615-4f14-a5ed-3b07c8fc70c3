// 英语
const messages = {
  common: {
    submit: 'submit',
    nodata: 'No data yet~'
  },
  search: {
    title: 'search',
    sousuolishi: 'Search History',
    tip1: 'No data yet',
    tip2: 'loading...',
    tip3: 'Search Stock Codes'
  },
  real_name: {
    realName: 'Real-name authentication',
    tip1: 'Passed real-name authentication, please do not submit again',
    tip2: 'Real-name authentication is under review',
    tip3: 'Real-name authentication has been passed',
    tip4: 'Upload failed',
    tip5: 'Front upload successful',
    tip6: 'Back side uploaded successfully',
    tip7: 'Real-name authentication completed',
    tip8: 'ID card name',
    tip9: 'Please enter your ID card name',
    tip10: 'ID number',
    tip11: 'Please enter your ID number',
    tip12: 'Upload the front of your ID',
    tip13: 'Upload the back of your ID',
    tip14: 'submit',
    tip15: 'Reason for rejection: Your real-name authentication has been rejected because',
    tip16: 'The picture is being uploaded...'
  },
  details: {
    xiaoxi: 'Details'
  },
  seting: {
    sheding: 'set up',
    zhanghu: 'Account',
    xingming: 'Name',
    denglumima: 'Login Password',
    ziji<PERSON><PERSON><PERSON>: 'Fund password',
    logout: 'Sign out'
  },
  yuyan: {
    ribenyu: 'Japanese',
    zhongwenjianti: 'Chinese (Simplified)',
    yinyu: 'English',
    xibanyayu: 'Spanish',
    fayu: 'French'
  },
  withdrawal: {
    zhanghuyue: 'Account Balance',
    tilingjine: 'Withdrawal amount',
    jiaoyimima: 'Transaction password',
    queren: 'confirm',
    zhuyishixiang: 'Things to note when withdrawing',
    tip1: 'Please enter the withdrawal amount',
    tip2: 'Please enter your transaction password',
    tixian: 'Withdraw to',
    chikaren: 'cardholder',
    title: 'Select bank account',
    tip3: 'Add bank account',
    tip4: 'Please select a bank account',
    tip5: 'Please enter the withdrawal amount',
    tip6: 'Please enter the transaction password',
    tip7: 'Amount: From 10,000 yen',
    tip8: 'Fee: Free',
    tip9: 'Withdrawal time: Weekdays 9:00-15:30',
    tip10: 'Withdrawals will arrive within 24 hours'
  },
  recharge: {
    chongzhijine: 'Recharge amount',
    queren: 'confirm',
    zhuyishixiang: 'Things to note when recharging'
  },
  toastText: {
    chenggong: 'Request Success',
    shibai: 'Request failed',
    tip1: 'Information loading...'
  },
  order: {
    tip1: 'Stock Details'
  },
  tabbar: {
    index: 'Home',
    market: 'market',
    jiaoyi: 'trade',
    record: 'Information',
    user: 'personal'
  },
  index: {
    gongneng: 'Function',
    hot: 'Hot Stocks',
    button1: 'IPO',
    button2: 'customer service',
    button3: 'Accounts',
    button4: 'Transaction Details',
    tip1: 'Unknown',
    tip2: 'detailed'
  },
  gupiaoDetail: {
    title: 'Stock Details',
    label1: 'Open today',
    label2: "Yesterday's harvest",
    label3: 'Highest',
    label4: 'lowest',
    label5: 'Volume',
    label6: 'Total transaction price',
    date1: 'Time Series',
    date2: 'Japanese K',
    date3: '7 points',
    date4: '30 min',
    shijia: 'market price',
    xianjia: 'Limit Price',
    maiduo: 'Buy more',
    maikong: 'Buy Short',
    submit: 'submit',
    inputLabel1: 'price',
    inputLabel2: 'Unit(share)',
    inputLabel3: 'Leverage (times)',
    windowLabel1: 'Commission Price',
    windowLabel2: 'Entrusted quantity (shares)',
    windowLabel3: 'principal',
    windowLabel4: 'Market Cap',
    windowLabel5: 'Handling Fees',
    windowLabel6: 'total',
    windowLabel7: 'Available balance',
    tip1: 'time',
    tip2: 'Choose a time',
    tip3: '1 min',
    tip4: '5 min',
    tip5: '15 min',
    tip6: '30 min',
    tip7: '45 min',
    tip8: 'sky',
    tip9: 'week',
    tip10: 'month',
    tip11: 'purchase record',
    tip12: 'return',
    yi: '100 million'
  },
  message: {
    xiaoxi: 'information'
  },
  jiaoyi: {
    title: 'Accounts',
    total: 'Profit and loss·Market value, etc.',
    weishixiansunyi: 'Unrealized gains and losses',
    yishixiansunyi: 'Realized profit and loss',
    sunyiheji: 'Total estimated profit and loss',
    shizhiheji: 'Total market value',
    sunyiheji2: 'Realized profit and loss',
    sunyiheji3: 'New stock lottery',
    shizhihej2i: 'Total market value (realized profits and losses)',
    yue: 'Available balance',
    dingdanxiangqing: 'Order details',
    xianzaixianqging: 'Details now',
    shuliang: 'Quantity (pieces)',
    chigushu: 'Number of shares held',
    goumaijiage: 'buying price',
    xianzaijiage: 'selling price',
    xianjia: 'current price',
    shouxufei: 'handling fee',
    sunyilv: 'profit and loss ratio',
    goumaizongjia: 'total cost',
    xianzaizongjia: 'total market capitalization',
    shijian: 'time',
    yugusunyi: 'Estimated profit and loss',
    yugusunyi2: 'Realize profit and loss',
    chicang: 'position',
    pingcang: 'Close position',
    gupiaoxiangqing: 'Stock details',
    maichu: 'Sell',
    chedan: 'Cancel order',
    tip1: 'Holding position',
    tip2: 'Closed position',
    tip3: 'Closing position',
    tip4: 'Pending order',
    tip5: 'Canceled order',
    tip6: 'Reminder',
    tip7: 'Are you sure about the sell order? ',
    tip8: 'Return',
    tip9: 'Confirm',
    tip10: 'Are you sure you want to cancel the order?',
    fangxiang: 'Buy direction',
    zhang: 'Up',
    die: 'Down',
    jiage: 'Price',
    zhognqianshuliang: 'Number of winning tickets',
    shenqingliang: 'Amount of application',
    chouqianri: 'Drawing date',
    faquanri: 'Issuance date',
    renjiaogushu: 'Number of subscribed shares',
    txt1: 'Winning tickets should be subscribed',
    txt2: 'Winning tickets have been subscribed',
    txt3: 'Waiting for winning tickets',
    txt4: 'Winning tickets have been won',
    txt5: 'Approved',
    txt6: 'Not winning tickets'
  },
  jiaoyiDetail: {
    title: 'Stock details',
    leixing: 'Status',
    danhao: 'Transaction number',
    mairushijian: 'Buy time',
    mairujia: 'Buy price',
    maichushijian: 'Sell time',
    maichujia: 'Sell price',
    shuliang: 'Number of shares traded',
    shuliang1: 'Buying Quantity',
    yingkui: 'Profit and loss amount',
    mairuzongjia: 'Transaction market value',
    ganggan: 'Leverage multiple',
    zuizhongjiage: 'Final price',
    shouxufei: 'Handling fee'
  },
  market: {
    gupiao: 'stock market',
    collect: 'Collection'
  },
  ipo: {
    title: 'IPO',
    title1: 'Lottery application resume',
    title2: 'Election Resume',
    button1: 'can subscribe',
    button2: 'Application record',
    jiage: 'underwriting price',
    shijia: 'market price',
    shuliang: 'Public fundraising number',
    shijian: 'drawing day',
    windowLabel1: 'Public price',
    windowLabel2: 'Subscription quantity',
    windowLabel3: 'Total amount',
    zang: 'piece'
  },
  dadan: {
    title: 'Block Trades ',
    title2: 'Block Trades',
    column_name: 'Stock name',
    column_daima: 'Stock code',
    column_xianjia: 'Current price',
    column_jiage: 'Hedging price',
    column_zhangshu: 'Number of remaining sheets',
    heji: 'total',
    zhuyi: 'attention',
    unit: 'open',
    unit2: 'share',
    tip1: 'Subscription successful',
    tip2: 'Subscription failed',
    tip3: 'Applying'
  },
  hongli: {
    title: 'Pre-market trading',
    title2: 'Pre-market trading',
    button1: 'Stock',
    button2: 'Application list',
    column_name: 'Stock name',
    column_jiage: 'subscription price',
    column_daima: 'Stock code',
    column_name2: 'name/stock code',
    column_jiage2: 'Subscription price/Application volume',
    column_chengjiaoe: 'Transaction volume',
    column_zhuangtai: 'status',
    window_bianma: 'encoding',
    window_shijia: 'market price',
    window_zhangdie: 'up or down',
    window_heji: 'Total',
    window_yue: 'Available balance',
    submit: 'Confirm purchase',
    tip1: 'Subscription successful',
    tip2: 'Subscription failed',
    tip3: 'Applying'
  },
  gendan: {
    title: 'Smart follow-up',
    fadanren: 'issuer',
    name: 'product name',
    zijinguimo: 'Capital scale',
    jiezhishijian: 'Application deadline: ',
    zuiditouzi: 'minimum investment',
    mairujine: 'Buy amount',
    button: 'Follow orders',
    jilu: 'record',
    yingli: 'profit',
    warning1: 'Please enter the purchase price',
    warning2: 'The purchase price can only have two decimal places',
    placeholder: 'Please enter the purchase amount'
  },
  gendanjilu: {
    title: 'Following records',
    dingdanhao: 'order number',
    mairujine: 'Buy amount',
    zuorishouyi: "Yesterday's earnings",
    zongshouyi: 'total income',
    zhuangtai: 'status',
    mairushijian: 'Buy time',
    maichushijian: 'selling time',
    button: 'Sell',
    window_title: 'Are you sure to sell?',
    status1: 'pending review',
    status2: 'Following order',
    status3: 'Following order ended'
  },
  rongzi: {
    title: 'Credit Financing',
    tip1: 'Quick Approval',
    tip2: 'Fast processing of credit applications',
    tip3: 'Flexible repayment',
    tip4: 'Various repayment plans to meet your personal situation',
    tip5: 'Transparent interest rate',
    tip6: 'We are committed to providing transparent and fair interest rates',
    tip7: 'Application process',
    tip8: 'Fill in the form: Fill in the form below and provide basic information',
    tip9: 'Review process: Our team will quickly review your application and contact you',
    tip10: 'Approval and disbursement: Once approved, you will receive credit approval and disbursement quickly',
    tip11: 'Loan amount',
    tip12: 'Loan amount of 3 million yen',
    tip13: 'Basic personal information',
    tip14: 'Please enter your name',
    tip15: 'name',
    tip16: 'ID card number',
    tip17: 'Please enter your ID number',
    tip18: 'address',
    tip19: 'Please enter address',
    tip20: 'Contact number',
    tip21: 'Please enter your contact number',
    tip22: 'email address',
    tip23: 'Please enter your email address',
    tip24: 'Apply now',
    tip25: 'Another loan',
    tip26: 'Reapply',
    tip27: 'The information has been submitted for review',
    tip28: 'Loan approved',
    tip29: 'Loan review failed',
    tip30: 'Congratulations on your successful submission, waiting for approval',
    tip31: 'You will get credit approval and funding quickly',
    tip32: 'You do not meet the application conditions yet, please apply again',
    tip33: 'Minimum loan 500,000',
    tip34: 'The maximum loan available is 3 million',
    tip35: 'Missing information',
    tip36: 'Contact customer service',
    tip37: 'Return to homepage',
    tip38: 'Single credit limit',
    tip39: '50-300',
    tip40: 'Ten thousand'
  },
  orderLog: {
    money: 'amount',
    beizhu: 'remarks'
  },
  customerService: {
    title: 'Please select the APP to use when contacting APP name',
    shiyong: 'Use',
    tip: 'You can talk to the other party in LINE APP. ',
    tip2: 'You can use collaborative features such as free calls, video conferencing and schedule sharing. '
  },
  logout: {
    registertip: 'Register account',
    logotip: 'I have an account'
  },
  transactionLog: {
    tab1: 'Order history',
    tab2: 'Deposit record',
    tab3: 'Withdrawal record',
    tip1: 'Order number',
    tip2: 'Awaiting review',
    tip3: 'Successful audit',
    tip4: 'Audit failed'
  },
  incomeLog: {
    yipizhun: 'Approved',
    daishenhe: 'to be reviewed',
    shenheshibai: 'Audit failed',
    dingdanbianhao: 'Order number',
    shourujilu: 'Deposit record',
    zhichujilu: 'Withdrawal record'
  },
  changePassword: {
    xiugaidenglumima: 'Change login password',
    jiumima: 'old password',
    xingmima: 'new password',
    queren: 'confirm',
    tip1: 'Please enter the current password',
    tip2: 'Please enter a new password',
    tip3: 'Please fill in a new password with a length of 6~10 digits',
    tip4: 'The new password and the confirmed new password are inconsistent'
  },
  user: {
    xinyongpingfen: 'Information score',
    yiqueren: 'Settings',
    zongzichan: 'Total assets',
    xianjinyue: 'Available balance',
    beiyongzijin: 'Used funds',
    fudongsunyi: 'Floating profit and loss',
    yinhangzhanghu: 'Bank account',
    shouru: 'Deposit and withdrawal records',
    benrenqueren: 'Real-name authentication',
    rizhisuoyin: 'Change login password',
    shiwuchuli: 'Change payment password',
    kefuzhongxin: 'Customer service center',
    qiehuanyuyan: 'Switch language',
    zhuxiao: 'Log out',
    chongzhi: 'Recharge',
    tixian: 'Withdrawal',
    tip1: 'Please contact your designated customer service representative to complete the deposit process.'
  },
  account: {
    cunquzhanghaodengji: 'Deposit and withdrawal account registration',
    chatu: 'illustration',
    zhanghaoming: 'account name',
    zhanghao: 'account',
    yinghangming: 'bank name',
    fenhangmingceng: 'branch name',
    fenhangbianhap: 'branch number',
    cunquzhanghaodengjibtn: 'Deposit and withdrawal account registration'
  },
  checkMsg: {
    shuruxinming: 'Please enter your name',
    shuruzhanghao: 'Please enter your account number',
    shuruyinhangming: 'Please enter the bank name',
    shurufenhangming: 'Please enter the branch name',
    cunquzhanghaodengjibtn: 'Enter Jinkouzuo Login',
    shurufenhanghaomao: 'Please enter the branch number',
    shuruxingmima: 'Please enter a new password',
    shurujiumima: 'Please enter the old password'
  },
  transactionPassions: {
    shezhishiwuchulitoushi: 'Set password',
    jiumima: 'old password',
    xingmima: 'new password',
    xingmima2: 'Confirm new password',
    queren: 'confirm',
    tip1: 'Please enter the original withdrawal password',
    tip2: 'Please enter the new withdrawal password',
    tip3: 'Please fill in the new withdrawal password with a length of 6~10 digits',
    tip4: 'The new password and the confirmed password are inconsistent'
  },
  indexLoading: {
    label1: 'Starting...',
    label2: 'Quotation server connection successful...',
    label3: 'Checking for updates...',
    label4: 'Check update completed...',
    label5: 'Login to the system...',
    label6: 'Get quotation host information (GS1DNS)...'
  },
  login: {
    tip1: 'Log in',
    tip2: 'register',
    tip3: 'Login Account',
    account_placeholder: 'Account',
    account_error: 'Account cannot be empty',
    password_placeholder: 'Password',
    password_error: 'Password cannot be empty',
    forget_password: 'Forgot password',
    register: "Don't have an account?",
    button_text: 'Login',
    user_info_null: 'User information has expired, please log in again'
  },
  register: {
    tip3: 'Log in',
    tip4: '注册账号',
    account_placeholder: 'Mobile number',
    account_error: 'Mobile number cannot be empty',
    password_placeholder: 'Password',
    password_error: 'Password cannot be empty',
    password_again_placeholder: 'Confirm password',
    password_again_error: 'Confirm password cannot be empty',
    password_repeat_error: 'The passwords entered twice are different',
    code_placeholder: 'Invitation code',
    code_error: 'Invitation code cannot be empty',
    code_text: 'Get verification code',
    button_text: 'Register',
    button2_text: 'Already have an account? Log in now',
    tip1: 'Mobile number format is wrong, please re-enter',
    tip2: 'Password length must be greater than 6 and less than 10'
  },
  kefu: {
    title: 'customer service'
  }
}

export default messages
