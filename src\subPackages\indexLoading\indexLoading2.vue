<template>
  <div class="container">
    <img class="bg" :src="bg" />
    <div class="content">
      <div class="card card1">
        <img :src="icon1" alt="" />
        <div class="right">
          <div class="title">{{ t('indexLoading.title1') }}</div>
          <div class="bottom">
            <div class="info">{{ t('indexLoading.info1') }}</div>
            <div class="btn" @click="goPage('/subPackages/login/login')">{{ t('indexLoading.btn1') }}→</div>
          </div>
        </div>
      </div>
      <div class="card card2">
        <img :src="icon2" alt="" />
        <div class="right">
          <div class="title">{{ t('indexLoading.title2') }}</div>
          <div class="title">{{ t('indexLoading.title3') }}</div>
          <div class="bottom">
            <div class="info">{{ t('indexLoading.info2') }}</div>
            <div class="btn" @click="goPage('/subPackages/register/register')">{{ t('indexLoading.btn2') }}→</div>
          </div>
        </div>
      </div>
    </div>
    <!-- <view class="button" @click="goPage('/subPackages/login/login')">{{ t('indexLoading.btn1') }}</view> -->
  </div>
</template>

<script lang="ts" setup>
import { goPage } from '@/common/common'
import bg from '@/static/image/login/indexLoading.png'
import icon1 from '@/static/image/login/icon1.png'
import icon2 from '@/static/image/login/icon2.png'
import { onLoad } from '@dcloudio/uni-app'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

onLoad(() => {
  uni.removeStorageSync('token')
  uni.removeStorageSync('userId')
  uni.removeStorageSync('userInfo')
  uni.removeStorageSync('tabbarHeight')
})
</script>

<style lang="scss" scoped>
.container {
  height: calc(var(--vh) * 100);
  background: #000;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  overflow: hidden;
  .content {
    position: fixed;
    bottom: 1.25rem;
  }
  .bg {
    width: 100%;
    object-fit: contain;
    position: fixed;
    top: 0;
  }
  .card {
    position: relative;
    display: flex;
    padding: 0 1.88rem;
    margin-bottom: 0.94rem;
    img {
      width: 2.25rem;
      height: 2.25rem;
    }
    .title {
      color: #fff;
      margin-left: 0.63rem;
      min-height: 2.25rem;
      font-size: 1.05rem;
      display: flex;
      align-items: center;
      font-weight: bold;
    }
    .bottom {
      margin-top: 0.63rem;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .info {
        color: #787878;
        margin-left: 0.63rem;
        font-size: 0.94rem;
      }
      .btn {
        width: 12.5rem;
        height: 3.06rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 1.13rem;
        border-radius: 1.56rem;
        margin-top: 0.94rem;
      }
    }
  }
  .card1 {
    .bottom {
      position: relative;
      &::before {
        content: '';
        height: 100%;
        width: 0.13rem;
        background: $color-green;
        position: absolute;
        left: 0;
        top: 0;
        border-radius: 0.13rem;
      }
    }
    .btn {
      background: linear-gradient(90deg, #00df8b 0%, #86ffd1 100%);
    }
  }
  .card2 {
    position: relative;
    .bottom {
      position: relative;
      &::before {
        content: '';
        height: 100%;
        width: 0.13rem;
        background: $color-red;
        position: absolute;
        left: 0;
        top: 0;
        border-radius: 0.13rem;
      }
    }
    .btn {
      background: linear-gradient(90deg, #ff435c 0%, #ff4830 100%);
    }
  }
}
</style>
